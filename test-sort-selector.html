<!DOCTYPE html>
<html lang="bn-BD">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sort Selector Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .feature-header {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .feature-header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .recent-filter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sort-selector,
        .period-selector {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            padding: 6px 10px !important;
            border-radius: 4px !important;
            font-size: 0.9rem !important;
            min-width: 140px !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            position: relative !important;
            z-index: 9999 !important;
            -webkit-appearance: menulist !important;
            -moz-appearance: menulist !important;
            appearance: menulist !important;
        }
        
        .sort-selector:hover,
        .period-selector:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            cursor: pointer !important;
        }
        
        .sort-selector option,
        .period-selector option {
            background: #333 !important;
            color: white !important;
            padding: 8px !important;
        }
        
        .test-result {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #2ecc71;
        }
        
        .test-log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .browser-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Sort Selector Browser Compatibility Test</h1>
        
        <div class="browser-info">
            <h3>Browser Information:</h3>
            <p id="browserInfo">Loading...</p>
        </div>
        
        <div class="feature-header">
            <h2><i class="fas fa-history"></i> Test Sort Selector</h2>
            <div class="feature-header-controls">
                <div class="recent-filter-controls">
                    <select id="testSort" class="sort-selector" title="সাজানো">
                        <option value="date_desc">নতুন থেকে পুরাতন</option>
                        <option value="date_asc">পুরাতন থেকে নতুন</option>
                        <option value="amount_desc">বেশি থেকে কম টাকা</option>
                        <option value="amount_asc">কম থেকে বেশি টাকা</option>
                        <option value="category_asc">ক্যাটেগরি (ক-য)</option>
                        <option value="category_desc">ক্যাটেগরি (য-ক)</option>
                    </select>
                    <select id="testPeriod" class="period-selector">
                        <option value="7">গত ৭ দিন</option>
                        <option value="30" selected>গত ৩০ দিন</option>
                        <option value="90">গত ৯০ দিন</option>
                        <option value="all">সব লেনদেন</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="test-result">
            <h3>Test Results:</h3>
            <p>Sort Selector Value: <span id="sortValue">date_desc</span></p>
            <p>Period Selector Value: <span id="periodValue">30</span></p>
            <p>Last Event: <span id="lastEvent">None</span></p>
            <p>Event Count: <span id="eventCount">0</span></p>
        </div>
        
        <div class="test-log">
            <h4>Event Log:</h4>
            <div id="eventLog"></div>
        </div>
    </div>

    <script>
        // Browser detection
        const browser = {
            userAgent: navigator.userAgent,
            isChrome: /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor),
            isFirefox: /Firefox/.test(navigator.userAgent),
            isSafari: /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
            isEdge: /Edge/.test(navigator.userAgent),
            isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
        };
        
        // Display browser info
        document.getElementById('browserInfo').innerHTML = `
            User Agent: ${browser.userAgent}<br>
            Chrome: ${browser.isChrome}<br>
            Firefox: ${browser.isFirefox}<br>
            Safari: ${browser.isSafari}<br>
            Edge: ${browser.isEdge}<br>
            Mobile: ${browser.isMobile}
        `;
        
        let eventCount = 0;
        
        function logEvent(message) {
            eventCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('eventLog');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            document.getElementById('eventCount').textContent = eventCount;
            document.getElementById('lastEvent').textContent = message;
        }
        
        function updateValues() {
            const sortValue = document.getElementById('testSort').value;
            const periodValue = document.getElementById('testPeriod').value;
            
            document.getElementById('sortValue').textContent = sortValue;
            document.getElementById('periodValue').textContent = periodValue;
        }
        
        // Test event listeners
        document.addEventListener('DOMContentLoaded', () => {
            logEvent('DOM Content Loaded');
            
            const sortSelector = document.getElementById('testSort');
            const periodSelector = document.getElementById('testPeriod');
            
            // Multiple event types for better compatibility
            ['change', 'input', 'click', 'mousedown', 'touchstart'].forEach(eventType => {
                sortSelector.addEventListener(eventType, (e) => {
                    logEvent(`Sort Selector ${eventType} event`);
                    if (eventType === 'change') {
                        updateValues();
                    }
                });
                
                periodSelector.addEventListener(eventType, (e) => {
                    logEvent(`Period Selector ${eventType} event`);
                    if (eventType === 'change') {
                        updateValues();
                    }
                });
            });
            
            // Initial values
            updateValues();
            logEvent('Event listeners attached');
        });
        
        window.addEventListener('load', () => {
            logEvent('Window loaded');
        });
    </script>
</body>
</html>
