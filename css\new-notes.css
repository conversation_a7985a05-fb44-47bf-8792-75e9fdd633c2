/* New Notes System Styles */
.notes-container {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Header Styles */
.notes-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notes-subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
    display: block;
}

.add-note-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.add-note-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Notes Controls */
.notes-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-selector {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 20px !important;
    font-size: 0.9rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    pointer-events: auto !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
    appearance: menulist !important;
    z-index: 1000 !important;
    position: relative !important;
}

.sort-selector:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    cursor: pointer !important;
}

.sort-selector:focus {
    outline: none !important;
    cursor: pointer !important;
}

.sort-selector:active {
    cursor: pointer !important;
}

.sort-selector option {
    background: #333 !important;
    color: white !important;
    cursor: pointer !important;
}

/* Notes Grid */
.notes-grid {
    flex: 1;
    padding: 30px;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* Fixed 4 columns */
    gap: 15px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.1);
    /* Prevent layout shift during updates */
    contain: layout style;
    will-change: auto;
    transform: translateZ(0); /* Force hardware acceleration */
    transition: none; /* Disable transitions during updates */
}

/* Note Card */
.note-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    min-height: 160px;
    max-height: 200px;
    display: flex;
    flex-direction: column;
    /* Prevent layout shift */
    contain: layout style;
    will-change: transform;
}

.note-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.note-header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.note-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
}

.note-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    word-break: break-word;
}

.note-priority {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    white-space: nowrap;
}

.note-priority.low { background: #d4edda; color: #155724; }
.note-priority.medium { background: #fff3cd; color: #856404; }
.note-priority.high { background: #f8d7da; color: #721c24; }
.note-priority.urgent { background: #f5c6cb; color: #721c24; font-weight: 600; }

.note-dates {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 0.65rem;
    color: #7f8c8d;
}

.note-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.note-date i {
    font-size: 0.6rem;
    opacity: 0.7;
}

.note-preview {
    flex: 1;
    color: #5a6c7d;
    font-size: 0.8rem;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    margin-bottom: 10px;
}

.note-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 5px 0;
}

.note-actions {
    display: flex;
    justify-content: flex-end;
    gap: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.note-card:hover .note-actions {
    opacity: 1;
}

.note-action-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #6c757d;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.note-action-btn:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

.note-action-btn.delete:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* Note Editor Modal */
.note-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.note-editor-modal.show {
    display: flex;
    animation: modalFadeIn 0.3s ease;
}

.note-editor-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 1200px;
    min-width: 800px;
    height: 80%;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    resize: both;
    position: relative;
}

.note-editor-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.note-title-section {
    flex: 1;
    display: flex;
    gap: 15px;
    align-items: center;
}

.note-title-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.priority-selector {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    backdrop-filter: blur(10px);
    min-width: 120px;
}

.priority-selector option {
    background: #333;
    color: white;
}

.note-title-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.note-title-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.3);
}

.note-editor-actions {
    display: flex;
    gap: 10px;
}

.editor-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 12px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

.editor-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.editor-btn.close-btn:hover {
    background: rgba(220, 53, 69, 0.8);
    border-color: rgba(220, 53, 69, 0.8);
}

.editor-btn.fullscreen-btn {
    background: rgba(40, 167, 69, 0.2);
    border-color: rgba(40, 167, 69, 0.3);
}

.editor-btn.fullscreen-btn:hover {
    background: rgba(40, 167, 69, 0.8);
    border-color: rgba(40, 167, 69, 0.8);
}

.editor-btn.fullscreen-btn i {
    transition: transform 0.3s ease;
}

.note-editor-modal.fullscreen .editor-btn.fullscreen-btn {
    background: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.3);
}

.note-editor-modal.fullscreen .editor-btn.fullscreen-btn:hover {
    background: rgba(255, 193, 7, 0.8);
    border-color: rgba(255, 193, 7, 0.8);
}

/* Fullscreen Mode */
.note-editor-modal.fullscreen {
    z-index: 20000;
}

.note-editor-modal.fullscreen .note-editor-content {
    width: 100%;
    height: 100%;
    max-width: none;
    border-radius: 0;
    animation: none;
}

.note-editor-modal.fullscreen .jodit-editor-container {
    padding: 20px 30px;
}

.note-editor-modal.fullscreen .jodit-editor-container .jodit-container {
    height: calc(100vh - 250px) !important;
}

.note-editor-modal.fullscreen .jodit-editor-container .jodit-workplace {
    height: calc(100vh - 300px) !important;
}

.note-editor-modal.fullscreen .jodit-editor-container .jodit-wysiwyg {
    height: calc(100vh - 350px) !important;
    min-height: calc(100vh - 350px) !important;
}

/* Enhanced responsive editor */
.jodit-editor-container .jodit-container {
    transition: all 0.3s ease;
}

.jodit-editor-container .jodit-wysiwyg {
    transition: all 0.3s ease;
    resize: vertical;
    overflow-y: auto;
}

/* Jodit Editor Container */
.jodit-editor-container {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    /* Prevent layout shift */
    contain: layout style;
    will-change: auto;
}

.jodit-editor-container .jodit-container {
    height: 450px;
}

.jodit-editor-container .jodit-workplace {
    height: 400px;
}

.jodit-editor-container .jodit-wysiwyg {
    height: 350px !important;
    min-height: 350px !important;
}

/* Status Bar */
.editor-status-bar {
    background: #f8f9fa;
    padding: 12px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
    font-size: 0.85rem;
    color: #6c757d;
}

.status-left {
    display: flex;
    gap: 20px;
}

.status-right {
    display: flex;
    gap: 20px;
    align-items: center;
}

.auto-save-status {
    color: #28a745;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

/* Selection Popup Toolbar - Compact Context Menu Style */
.selection-popup-toolbar {
    position: fixed;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 6px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    display: none;
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-width: 200px;
    max-width: 250px;
    pointer-events: none;
    flex-direction: column;
}

.selection-popup-toolbar.show {
    display: flex;
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
}

.popup-toolbar-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2px;
    padding: 2px;
    width: 100%;
    flex-wrap: wrap;
}

.popup-toolbar-btn {
    background: transparent;
    border: none;
    color: #333;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
    position: relative;
}

.popup-toolbar-btn:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.popup-toolbar-btn:active {
    transform: scale(0.95);
}

.popup-toolbar-btn.active {
    background: #1976d2;
    color: white;
}

.popup-toolbar-btn i {
    font-size: 12px;
}

.popup-toolbar-btn span {
    display: none;
}

/* Tooltip for buttons */
.popup-toolbar-btn::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s ease;
    margin-bottom: 5px;
    z-index: 1001;
}

.popup-toolbar-btn:hover::after {
    opacity: 1;
    margin-bottom: 8px;
}

/* Dark mode support */
[data-theme="dark"] .selection-popup-toolbar {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .popup-toolbar-btn {
    color: #f8f9fa;
}

[data-theme="dark"] .popup-toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #74b9ff;
}

[data-theme="dark"] .popup-toolbar-btn.active {
    background: #1976d2;
    color: white;
}

[data-theme="dark"] .popup-toolbar-btn::after {
    background: rgba(0, 0, 0, 0.9);
    color: white;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes noteCardIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.note-card.new {
    animation: noteCardIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Image Controls */
.image-controls {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.jodit-editor img:hover + .image-controls,
.image-controls:hover {
    opacity: 1;
}

/* Enhanced Jodit Editor Styles */
.jodit-editor-container .jodit-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.jodit-editor-container .jodit-toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.jodit-editor-container .jodit-workplace {
    background: white;
}

/* Custom scrollbar for notes grid */
.notes-grid::-webkit-scrollbar {
    width: 8px;
}

.notes-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.notes-grid::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.notes-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Loading animation for new notes */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.note-card.loading {
    animation: pulse 1.5s infinite;
}

/* Enhanced hover effects */
.note-card:hover .note-title {
    color: #667eea;
}

.note-card:hover::before {
    height: 6px;
}

/* Better focus states */
.note-title-input:focus,
.jodit-editor-container .jodit-container:focus-within {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Dark Mode Support */
[data-theme="dark"] .notes-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .notes-header {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

[data-theme="dark"] .notes-grid {
    background: rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .note-card {
    background: #3a3a3a;
    color: #e0e0e0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .note-card:hover {
    background: #404040;
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .note-title {
    color: #f0f0f0;
}

[data-theme="dark"] .note-card:hover .note-title {
    color: #667eea;
}

[data-theme="dark"] .note-date {
    color: #b0b0b0;
}

[data-theme="dark"] .note-preview {
    color: #c0c0c0;
}

[data-theme="dark"] .note-action-btn {
    background: #4a4a4a;
    border-color: #555;
    color: #e0e0e0;
}

[data-theme="dark"] .note-action-btn:hover {
    background: #555;
    color: #f0f0f0;
}

[data-theme="dark"] .note-editor-content {
    background: #2c2c2c;
}

[data-theme="dark"] .note-title-input {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

[data-theme="dark"] .editor-status-bar {
    background: #3a3a3a;
    border-color: #555;
    color: #c0c0c0;
}

[data-theme="dark"] .sort-selector {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
}

[data-theme="dark"] .sort-selector option {
    background: #2c2c2c;
    color: white;
}

[data-theme="dark"] .priority-selector {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
}

[data-theme="dark"] .priority-selector option {
    background: #2c2c2c;
    color: white;
}

[data-theme="dark"] .note-dates {
    color: #b0b0b0;
}

[data-theme="dark"] .jodit-editor-container .jodit-toolbar {
    background: #3a3a3a !important;
    border-color: #555 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-workplace {
    background: #2c2c2c !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-wysiwyg {
    background: #2c2c2c !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-container {
    background: #2c2c2c !important;
    border-color: #555 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-toolbar-button {
    background: transparent !important;
    color: #e0e0e0 !important;
    border-color: #555 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-toolbar-button:hover {
    background: #4a4a4a !important;
    color: #f0f0f0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-toolbar-button.jodit-toolbar-button_active {
    background: #667eea !important;
    color: white !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-status-bar {
    background: #3a3a3a !important;
    border-color: #555 !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg p {
    color: #e0e0e0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h1,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h2,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h3,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h4,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h5,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg h6 {
    color: #f0f0f0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg ul,
[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg ol {
    color: #e0e0e0 !important;
}

[data-theme="dark"] .jodit-editor-container .jodit-workplace .jodit-wysiwyg blockquote {
    background: #3a3a3a !important;
    border-left-color: #667eea !important;
    color: #e0e0e0 !important;
}

/* Alternative dark theme class for Jodit */
.jodit-dark-theme .jodit-toolbar {
    background: #3a3a3a !important;
    border-color: #555 !important;
}

.jodit-dark-theme .jodit-workplace {
    background: #2c2c2c !important;
}

.jodit-dark-theme .jodit-wysiwyg {
    background: #2c2c2c !important;
    color: #e0e0e0 !important;
}

.jodit-dark-theme .jodit-container {
    background: #2c2c2c !important;
    border-color: #555 !important;
}

.jodit-dark-theme .jodit-toolbar-button {
    background: transparent !important;
    color: #e0e0e0 !important;
    border-color: #555 !important;
}

.jodit-dark-theme .jodit-toolbar-button:hover {
    background: #4a4a4a !important;
    color: #f0f0f0 !important;
}

.jodit-dark-theme .jodit-toolbar-button.jodit-toolbar-button_active {
    background: #667eea !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .notes-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .notes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .notes-grid {
        grid-template-columns: 1fr;
        padding: 20px;
        gap: 15px;
    }

    .note-editor-content {
        width: 95%;
        height: 95%;
        min-width: 300px;
        resize: none;
    }

    .note-editor-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }

    .note-title-section {
        flex-direction: column;
        gap: 10px;
    }

    .note-title-input {
        width: 100%;
    }

    .priority-selector {
        width: 100%;
        min-width: auto;
    }

    .note-editor-actions {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .notes-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .notes-controls {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .sort-selector {
        width: 100%;
    }

    .header-left h2 {
        font-size: 1.5rem;
    }

    .note-card {
        min-height: 160px;
        max-height: 200px;
    }

    .note-actions {
        flex-direction: column;
        gap: 5px;
    }

    .note-action-btn {
        width: 100%;
        justify-content: center;
    }

    .jodit-editor-container {
        padding: 15px;
    }

    .jodit-editor-container .jodit-wysiwyg {
        min-height: calc(100vh - 400px) !important;
        max-height: calc(100vh - 300px) !important;
    }

    .note-editor-modal.fullscreen .jodit-editor-container .jodit-wysiwyg {
        min-height: calc(100vh - 320px) !important;
        max-height: calc(100vh - 320px) !important;
        height: calc(100vh - 320px) !important;
    }

    .editor-btn {
        padding: 8px 10px;
        font-size: 0.9rem;
        margin: 2px;
    }

    /* Selection popup responsive */
    .selection-popup-toolbar {
        min-width: 180px;
        max-width: 90vw;
    }

    .popup-toolbar-group {
        gap: 1px;
    }

    .popup-toolbar-btn {
        padding: 5px 6px;
        font-size: 12px;
        min-width: 24px;
        height: 24px;
    }

    .popup-toolbar-btn i {
        font-size: 10px;
    }

    .popup-toolbar-btn::after {
        font-size: 10px;
        padding: 3px 6px;
    }
}
