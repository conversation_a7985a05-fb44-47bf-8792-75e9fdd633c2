/**
 * Sort Selector Browser Compatibility Fix
 * This script specifically addresses browser compatibility issues with select elements
 * in the feature headers, particularly for Chrome and other browsers.
 */

(function() {
    'use strict';
    
    console.log('Sort Selector Fix: Initializing...');
    
    // Configuration
    const CONFIG = {
        selectors: [
            '.sort-selector',
            '.period-selector',
            '#recentTransactionSort',
            '#recentTransactionPeriod',
            '#incomeHistorySort',
            '#incomeHistoryPeriod',
            '#expenseHistorySort',
            '#expenseHistoryPeriod',
            '#loanGivenHistorySort',
            '#loanGivenHistoryPeriod',
            '#loanTakenHistorySort',
            '#loanTakenHistoryPeriod',
            '#depositHistorySort',
            '#depositHistoryPeriod',
            '#withdrawHistorySort',
            '#withdrawHistoryPeriod'
        ],
        retryAttempts: 5,
        retryDelay: 500
    };
    
    // Browser detection
    const browser = {
        isChrome: /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor),
        isFirefox: /Firefox/.test(navigator.userAgent),
        isSafari: /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
        isEdge: /Edge/.test(navigator.userAgent),
        isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    };
    
    console.log('Sort Selector Fix: Browser detected:', browser);
    
    // Force select element to be interactive
    function forceSelectInteractivity(selectElement) {
        if (!selectElement) return false;

        try {
            console.log('Sort Selector Fix: Processing element', selectElement.id);

            // First, try direct fixes without cloning
            selectElement.disabled = false;
            selectElement.style.display = 'block';

            // Remove any existing event listeners that might interfere
            const newElement = selectElement.cloneNode(true);
            selectElement.parentNode.replaceChild(newElement, selectElement);
            
            // Apply aggressive styling
            const styles = {
                'pointer-events': 'auto',
                'cursor': 'pointer',
                'position': 'relative',
                'z-index': '9999',
                'background': 'rgba(255, 255, 255, 0.2)',
                'border': '1px solid rgba(255, 255, 255, 0.3)',
                'color': 'white',
                'padding': '6px 10px',
                'border-radius': '4px',
                'font-size': '0.9rem',
                'min-width': '140px',
                'outline': 'none'
            };
            
            // Browser-specific appearance fixes
            if (browser.isChrome || browser.isEdge) {
                styles['-webkit-appearance'] = 'menulist';
                styles['appearance'] = 'menulist';
            } else if (browser.isFirefox) {
                styles['-moz-appearance'] = 'menulist';
                styles['appearance'] = 'menulist';
            } else if (browser.isSafari) {
                styles['-webkit-appearance'] = 'menulist';
                styles['appearance'] = 'menulist';
            }
            
            // Apply styles
            Object.keys(styles).forEach(property => {
                newElement.style.setProperty(property, styles[property], 'important');
            });
            
            // Force enable the element
            newElement.disabled = false;
            newElement.style.display = 'block';
            
            // Add comprehensive event handling
            const events = ['change', 'input', 'click', 'mousedown', 'touchstart'];
            
            events.forEach(eventType => {
                newElement.addEventListener(eventType, function(e) {
                    console.log(`Sort Selector Fix: ${eventType} event triggered on`, this.id);
                    
                    if (eventType === 'change') {
                        // Trigger the appropriate update function
                        const elementId = this.id;
                        
                        if (window.moneyManager) {
                            if (elementId.includes('recentTransaction')) {
                                window.moneyManager.updateRecentTransactions();
                            } else if (elementId.includes('incomeHistory')) {
                                window.moneyManager.displayIncomeHistory();
                            } else if (elementId.includes('expenseHistory')) {
                                window.moneyManager.displayExpenseHistory();
                            } else if (elementId.includes('loanGivenHistory')) {
                                window.moneyManager.displayLoanHistory();
                            } else if (elementId.includes('loanTakenHistory')) {
                                window.moneyManager.displayLoanHistory();
                            } else if (elementId.includes('depositHistory')) {
                                window.moneyManager.displayBankHistory();
                            } else if (elementId.includes('withdrawHistory')) {
                                window.moneyManager.displayBankHistory();
                            }
                        }
                    }
                    
                    // Prevent event bubbling issues
                    e.stopPropagation();
                }, { passive: false });
            });
            
            // Special handling for mobile devices
            if (browser.isMobile) {
                newElement.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    this.focus();
                    this.click();
                }, { passive: false });
            }

            // Test if the element is actually clickable
            setTimeout(() => {
                const testClickable = () => {
                    let isClickable = false;
                    let changeTriggered = false;

                    const testClickHandler = () => {
                        isClickable = true;
                        console.log('Sort Selector Fix: Click test successful for', newElement.id);
                    };

                    const testChangeHandler = () => {
                        changeTriggered = true;
                        console.log('Sort Selector Fix: Change test successful for', newElement.id);
                    };

                    newElement.addEventListener('click', testClickHandler, { once: true });
                    newElement.addEventListener('change', testChangeHandler, { once: true });

                    // Test if element can receive focus
                    try {
                        newElement.focus();
                        console.log('Sort Selector Fix: Focus test successful for', newElement.id);
                    } catch (e) {
                        console.log('Sort Selector Fix: Focus test failed for', newElement.id, e);
                    }

                    // Simulate click
                    const clickEvent = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });

                    newElement.dispatchEvent(clickEvent);

                    // Test change event by programmatically changing value
                    const originalValue = newElement.value;
                    const options = newElement.options;
                    if (options && options.length > 1) {
                        const newValue = options[1].value;
                        newElement.value = newValue;

                        const changeEvent = new Event('change', { bubbles: true });
                        newElement.dispatchEvent(changeEvent);

                        // Restore original value
                        newElement.value = originalValue;
                    }

                    setTimeout(() => {
                        if (!isClickable && !changeTriggered) {
                            console.log('Sort Selector Fix: Element not responsive, creating custom dropdown for', newElement.id);
                            createCustomDropdown(newElement);
                        } else {
                            console.log('Sort Selector Fix: Element is responsive', newElement.id,
                                       'clickable:', isClickable, 'changeable:', changeTriggered);
                        }
                    }, 200);
                };

                testClickable();
            }, 300);

            console.log('Sort Selector Fix: Enhanced element', newElement.id);
            return true;
            
        } catch (error) {
            console.error('Sort Selector Fix: Error enhancing element', selectElement.id, error);
            return false;
        }
    }

    // Create custom dropdown as fallback
    function createCustomDropdown(originalSelect) {
        console.log('Sort Selector Fix: Creating custom dropdown for', originalSelect.id);

        const wrapper = document.createElement('div');
        wrapper.className = 'custom-select-wrapper';
        wrapper.style.cssText = `
            position: relative;
            display: inline-block;
            min-width: 140px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            cursor: pointer;
            z-index: 9999;
        `;

        const display = document.createElement('div');
        display.className = 'custom-select-display';
        display.style.cssText = `
            padding: 6px 10px;
            color: white;
            font-size: 0.9rem;
            user-select: none;
            position: relative;
        `;

        const arrow = document.createElement('span');
        arrow.innerHTML = '▼';
        arrow.style.cssText = `
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
            opacity: 0.7;
        `;

        const dropdown = document.createElement('div');
        dropdown.className = 'custom-select-dropdown';
        dropdown.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #333;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            z-index: 10000;
        `;

        // Get current value
        const currentValue = originalSelect.value;
        const currentText = originalSelect.options[originalSelect.selectedIndex]?.text || '';
        display.textContent = currentText;

        // Create options
        Array.from(originalSelect.options).forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'custom-select-option';
            optionDiv.textContent = option.text;
            optionDiv.dataset.value = option.value;
            optionDiv.style.cssText = `
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            `;

            if (option.value === currentValue) {
                optionDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            }

            optionDiv.addEventListener('mouseenter', () => {
                optionDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            });

            optionDiv.addEventListener('mouseleave', () => {
                if (option.value !== originalSelect.value) {
                    optionDiv.style.backgroundColor = 'transparent';
                }
            });

            optionDiv.addEventListener('click', (e) => {
                e.stopPropagation();

                // Update original select
                originalSelect.value = option.value;
                display.textContent = option.text;

                // Update option styles
                dropdown.querySelectorAll('.custom-select-option').forEach(opt => {
                    opt.style.backgroundColor = opt.dataset.value === option.value ?
                        'rgba(255, 255, 255, 0.2)' : 'transparent';
                });

                // Hide dropdown
                dropdown.style.display = 'none';
                arrow.innerHTML = '▼';

                // Trigger change event
                const changeEvent = new Event('change', { bubbles: true });
                originalSelect.dispatchEvent(changeEvent);

                console.log('Sort Selector Fix: Custom dropdown value changed to', option.value);
            });

            dropdown.appendChild(optionDiv);
        });

        // Toggle dropdown
        const toggleDropdown = (e) => {
            e.stopPropagation();
            const isVisible = dropdown.style.display === 'block';
            dropdown.style.display = isVisible ? 'none' : 'block';
            arrow.innerHTML = isVisible ? '▼' : '▲';
        };

        wrapper.addEventListener('click', toggleDropdown);
        display.addEventListener('click', toggleDropdown);

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!wrapper.contains(e.target)) {
                dropdown.style.display = 'none';
                arrow.innerHTML = '▼';
            }
        });

        // Assemble custom select
        display.appendChild(arrow);
        wrapper.appendChild(display);
        wrapper.appendChild(dropdown);

        // Replace original select
        originalSelect.style.display = 'none';
        originalSelect.parentNode.insertBefore(wrapper, originalSelect);

        console.log('Sort Selector Fix: Custom dropdown created for', originalSelect.id);
    }

    // Find and fix all select elements
    function fixAllSelectors() {
        let fixedCount = 0;
        
        CONFIG.selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            
            elements.forEach(element => {
                if (forceSelectInteractivity(element)) {
                    fixedCount++;
                }
            });
        });
        
        console.log(`Sort Selector Fix: Fixed ${fixedCount} select elements`);
        return fixedCount;
    }
    
    // Retry mechanism
    function retryFix(attempt = 1) {
        console.log(`Sort Selector Fix: Attempt ${attempt}/${CONFIG.retryAttempts}`);
        
        const fixedCount = fixAllSelectors();
        
        if (fixedCount === 0 && attempt < CONFIG.retryAttempts) {
            setTimeout(() => retryFix(attempt + 1), CONFIG.retryDelay);
        } else {
            console.log('Sort Selector Fix: Completed with', fixedCount, 'elements fixed');
        }
    }
    
    // Initialize when DOM is ready
    function initialize() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
            return;
        }
        
        console.log('Sort Selector Fix: DOM ready, starting fix...');
        retryFix();
        
        // Also fix when new content is added
        const observer = new MutationObserver((mutations) => {
            let shouldFix = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            const hasSelectors = CONFIG.selectors.some(selector => 
                                node.matches && node.matches(selector) || 
                                node.querySelector && node.querySelector(selector)
                            );
                            
                            if (hasSelectors) {
                                shouldFix = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldFix) {
                setTimeout(fixAllSelectors, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Immediate fix for recentTransactionSort
    function immediateRecentTransactionFix() {
        const recentSort = document.getElementById('recentTransactionSort');
        const recentPeriod = document.getElementById('recentTransactionPeriod');

        [recentSort, recentPeriod].forEach(element => {
            if (element) {
                console.log('Sort Selector Fix: Applying immediate fix to', element.id);

                // Force all styles
                element.style.setProperty('pointer-events', 'auto', 'important');
                element.style.setProperty('cursor', 'pointer', 'important');
                element.style.setProperty('z-index', '99999', 'important');
                element.style.setProperty('position', 'relative', 'important');
                element.style.setProperty('-webkit-appearance', 'menulist', 'important');
                element.style.setProperty('-moz-appearance', 'menulist', 'important');
                element.style.setProperty('appearance', 'menulist', 'important');
                element.disabled = false;

                // Add immediate event listener
                element.addEventListener('change', function(e) {
                    console.log('Sort Selector Fix: Immediate change event for', this.id, 'value:', this.value);

                    if (window.moneyManager) {
                        if (this.id === 'recentTransactionSort' || this.id === 'recentTransactionPeriod') {
                            console.log('Sort Selector Fix: Triggering updateRecentTransactions');
                            window.moneyManager.updateRecentTransactions();
                        }
                    }
                }, { passive: false });

                // Add click event for debugging
                element.addEventListener('click', function(e) {
                    console.log('Sort Selector Fix: Immediate click event for', this.id);
                }, { passive: false });

                console.log('Sort Selector Fix: Immediate fix applied to', element.id);
            }
        });
    }

    // Apply immediate fix
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', immediateRecentTransactionFix);
    } else {
        immediateRecentTransactionFix();
    }

    // Start initialization
    initialize();

    // Also run on window load as backup
    window.addEventListener('load', () => {
        setTimeout(() => {
            fixAllSelectors();
            immediateRecentTransactionFix();
        }, 1000);
    });
    
    // Manual trigger function for console testing
    window.triggerSortChange = function(selectorId, newValue) {
        const selector = document.getElementById(selectorId);
        if (selector) {
            console.log('Manual trigger: Changing', selectorId, 'to', newValue);
            selector.value = newValue;

            const changeEvent = new Event('change', { bubbles: true });
            selector.dispatchEvent(changeEvent);

            console.log('Manual trigger: Event dispatched for', selectorId);
        } else {
            console.log('Manual trigger: Selector not found:', selectorId);
        }
    };

    // Force fix function for console
    window.forceFixRecentSort = function() {
        console.log('Force fix: Applying to recentTransactionSort');
        const element = document.getElementById('recentTransactionSort');
        if (element) {
            forceSelectInteractivity(element);
            immediateRecentTransactionFix();
            console.log('Force fix: Applied to recentTransactionSort');
        } else {
            console.log('Force fix: recentTransactionSort not found');
        }
    };

    // Export for debugging
    window.sortSelectorFix = {
        version: '1.0.0',
        browser: browser,
        fixAllSelectors: fixAllSelectors,
        forceSelectInteractivity: forceSelectInteractivity,
        createCustomDropdown: createCustomDropdown,
        immediateRecentTransactionFix: immediateRecentTransactionFix
    };
    
})();
